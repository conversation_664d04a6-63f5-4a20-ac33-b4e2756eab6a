import { createApp } from 'vue'

import Cookies from 'js-cookie'
import 'normalize.css/normalize.css'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import { createHead } from '@unhead/vue/client'

import '@/styles/main.scss' // global css

import App from './App.vue'
import store from './store'
import router from './router'
import { createAllProviders } from './context'

import * as filters from './filters'

if (import.meta.env.PROD) {
  const { mockXHR } = await import('../mock')
  mockXHR()
}

const app = createApp(App)

// 创建所有 Context Providers
createAllProviders()

app.use(ElementPlus, {
  size: Cookies.get('size') || 'medium',
})

// / 兼容 Vue 2 使用的全局 filter， 即 xx
Object.keys(filters).forEach((key) => {
  app.config.globalProperties[key] = filters[key]
})
// / 兼容 Vue 2 使用的全局 filter， 即 $filter.xx
app.config.globalProperties.$filters = filters

app.config.globalProperties.routerAppend = (path, pathToAppend) => {
  return path + (path.endsWith('/') ? '' : '/') + pathToAppend
}

const head = createHead()

app.use(store)
app.use(router)
app.use(head)
app.mount('#app')

// 兼容 Vue 2 的全局
// window.$vueApp = app
